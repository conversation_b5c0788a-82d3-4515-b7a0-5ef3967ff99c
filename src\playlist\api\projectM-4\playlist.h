/**
 * @file playlist.h
 * @copyright 2003-2024 projectM Team
 * @brief Optional playlist API for libprojectM.
 * @since 4.0.0
 *
 * projectM -- Milkdrop-esque visualisation SDK
 * Copyright (C)2003-2024 projectM Team
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 * See 'LICENSE.txt' included within this release
 *
 */

#pragma once

#include "projectM-4/playlist_callbacks.h"
#include "projectM-4/playlist_core.h"
#include "projectM-4/playlist_filter.h"
#include "projectM-4/playlist_items.h"
#include "projectM-4/playlist_memory.h"
#include "projectM-4/playlist_playback.h"
#include "projectM-4/playlist_types.h"
