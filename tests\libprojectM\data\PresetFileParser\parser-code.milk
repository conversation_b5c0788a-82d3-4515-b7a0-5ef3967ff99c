// Very simple multi-line equation
per_frame_1=r=1.0;
per_frame_2=g=1.0;
per_frame_3=
per_frame_4=b=1.0;

// A gap in numbering should terminate parsing
per_frame_gap_1=r=1.0;
per_frame_gap_2=g=1.0;
per_frame_gap_4=b=1.0;

// Line number 2 is duplicated, should only use the first one
per_frame_repeat_1=r=1.0;
per_frame_repeat_2=g=1.0;
per_frame_repeat_2=pi=3.141;
per_frame_repeat_3=b=1.0;

// Some different comment formats used plus a lot of whitespace.
// Also contains an equation that spans two line.
per_frame_trim_1=  r = 1.0;
per_frame_trim_2= g = 1.0;
per_frame_trim_3=//****************** Comment format often seen in equations
per_frame_trim_4=               // Empty line with comment
per_frame_trim_5=    b = \\Continued line with backslash comment
per_frame_trim_6=          1.0;

// Comment spanning multiple lines, starting/ending at arbitrary positions
multiline_comment_1=r = 1.0; /* Comment...
multiline_comment_2=g = 1.0;
multiline_comment_3=... ends here */b = 1.0;

// Shader syntax
warp_1=`r=1.0;
warp_2=`g=1.0;
warp_3=`b=1.0;
