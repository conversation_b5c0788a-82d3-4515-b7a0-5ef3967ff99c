set(projectM4_VERSION @PROJECT_VERSION@)

@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

if(NOT "@ENABLE_EMSCRIPTEN@") # ENABLE_EMSCRIPTEN
    if("@ENABLE_GLES@") # ENABLE_GLES
        list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}")
        find_dependency(OpenGL COMPONENTS GLES3)
    else()
        find_dependency(OpenGL)
    endif()
endif()
if("@ENABLE_BOOST_FILESYSTEM@") # ENABLE_BOOST_FILESYSTEM
    find_dependency(Boost COMPONENTS Filesystem)
endif()
if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
     find_dependency(GLEW)
endif()

set(_projectM4_FIND_PARTS_REQUIRED)
if(projectM4_FIND_REQUIRED)
    set(_projectM4_FIND_PARTS_REQUIRED REQUIRED)
endif()
set(_projectM4_FIND_PARTS_QUIET)
if(projectM4_FIND_QUIETLY)
    set(_projectM4_FIND_PARTS_QUIET QUIET)
endif()

include("${CMAKE_CURRENT_LIST_DIR}/projectM4Targets.cmake")

if(projectM4_FIND_COMPONENTS)
    foreach(component ${projectM4_FIND_COMPONENTS})
        find_package(projectM4${component}
            ${_projectM4_FIND_PARTS_REQUIRED}
            ${_projectM4_FIND_PARTS_QUIET}
            )

        if(NOT projectM4${component}_FOUND)
            if (projectM4_FIND_REQUIRED_${component})
                set(_projectM4_NOTFOUND_MESSAGE "${_libprojectM_NOTFOUND_MESSAGE}Failed to find libprojectM component \"${component}\" config file\n")
            elseif(NOT projectM4_FIND_QUIETLY)
                message(WARNING "Failed to find projectM4 component \"${component}\" config file")
            endif()
        endif()
    endforeach()
endif()

if (_projectM4_NOTFOUND_MESSAGE)
    set(projectM4_NOT_FOUND_MESSAGE "${_libprojectM_NOTFOUND_MESSAGE}")
    set(projectM4_FOUND False)
endif()
