name: Windows (x64)

on:
  push:
      branches: 
        - "*"
      tags:
        - "*"
    
  pull_request:
      branches: 
        - "*"

jobs:
  build-shared:
    name: Shared Library
    runs-on: windows-latest

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'

      - name: Configure Build (MD)
        run: cmake -G "Visual Studio 17 2022" -A "X64" -S "${{ github.workspace }}" -B "${{ github.workspace }}/cmake-build" -DCMAKE_TOOLCHAIN_FILE="${Env:VCPKG_INSTALLATION_ROOT}/scripts/buildsystems/vcpkg.cmake" -DVCPKG_TARGET_TRIPLET=x64-windows -DCMAKE_INSTALL_PREFIX="${{ github.workspace }}/install" -DCMAKE_MSVC_RUNTIME_LIBRARY="MultiThreaded$<$<CONFIG:Debug>:Debug>DLL" -DCMAKE_VERBOSE_MAKEFILE=YES -DBUILD_SHARED_LIBS=ON -DBUILD_TESTING=YES

      - name: Build Debug
        run: cmake --build "${{ github.workspace }}/cmake-build" --config "Debug" --parallel

      - name: Run Unit Tests
        run: ctest --test-dir "${{ github.workspace }}/cmake-build" --verbose --build-config "Debug"

      - name: Build Release
        run: cmake --build "${{ github.workspace }}/cmake-build" --config "Release" --parallel

      - name: Install
        run: |
          cmake --build "${{ github.workspace }}/cmake-build" --config "Debug" --target INSTALL
          cmake --build "${{ github.workspace }}/cmake-build" --config "Release" --target INSTALL

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
            name: projectm-windows-shared-latest
            path: install/*

  build-static:
    name: Static Library
    runs-on: windows-latest

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'

      - name: Configure Build (MD)
        run: cmake -G "Visual Studio 17 2022" -A "X64" -S "${{ github.workspace }}" -B "${{ github.workspace }}/cmake-build" -DCMAKE_TOOLCHAIN_FILE="${Env:VCPKG_INSTALLATION_ROOT}/scripts/buildsystems/vcpkg.cmake" -DVCPKG_TARGET_TRIPLET=x64-windows-static -DCMAKE_INSTALL_PREFIX="${{ github.workspace }}/install" -DCMAKE_MSVC_RUNTIME_LIBRARY="MultiThreaded$<$<CONFIG:Debug>:Debug>" -DCMAKE_VERBOSE_MAKEFILE=YES -DBUILD_SHARED_LIBS=OFF -DBUILD_TESTING=YES

      - name: Build Debug
        run: cmake --build "${{ github.workspace }}/cmake-build" --config "Debug" --parallel

      - name: Run Unit Tests
        run: ctest --test-dir "${{ github.workspace }}/cmake-build" --verbose --build-config "Debug"

      - name: Build Release
        run: cmake --build "${{ github.workspace }}/cmake-build" --config "Release" --parallel

      - name: Install
        run: |
          cmake --build "${{ github.workspace }}/cmake-build" --config "Debug" --target INSTALL
          cmake --build "${{ github.workspace }}/cmake-build" --config "Release" --target INSTALL

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: projectm-windows-static-latest
          path: install/*
