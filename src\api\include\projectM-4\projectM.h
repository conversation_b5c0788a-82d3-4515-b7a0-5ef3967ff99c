/**
 * @file projectM.h
 * @copyright 2003-2024 projectM Team
 * @brief Convenience include file that includes all other API headers.
 * @since 4.0.0
 *
 * projectM -- Milkdrop-esque visualisation SDK
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 * See 'LICENSE.txt' included within this release
 *
 */

#pragma once

#include "projectM-4/audio.h"
#include "projectM-4/callbacks.h"
#include "projectM-4/core.h"
#include "projectM-4/debug.h"
#include "projectM-4/memory.h"
#include "projectM-4/parameters.h"
#include "projectM-4/render_opengl.h"
#include "projectM-4/touch.h"
#include "projectM-4/version.h"
#include "projectM-4/user_sprites.h"
