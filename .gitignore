build_logs/

xcuserdata
src/libprojectM/config.inp
src/libprojectM/config.inp.b
src/libprojectM/config.inp.in
src/projectM-emscripten/build/presets
presets/custom
project.xcworkspace
*.o
*.lo
*.la
*.a
projectM-*.tar.gz
.deps/
.libs/
Makefile
Makefile.in
/aclocal.m4
/autom4te.cache
/build-aux
/config.h
/config.h.in
/configure
/libtool
/stamp-h1
.dirstamp
/src/libprojectM/config.h
ar-lib
compile
depcomp
install-sh
ltmain.sh
missing
m4/libtool.m4
m4/ltoptions.m4
m4/ltsugar.m4
m4/ltversion.m4
m4/lt~obsolete.m4
/t
/src/libprojectM/libprojectM.pc
/build
/dist

# Visual Studio w/CMake
/out
/.vs
/CMakeSettings.json

_sync.bat
_sync.sh
to_sync/
.DS_Store
src/projectM-sdl/build/
src/libprojectM/build/
*.pkg
./vcpkg_installed

# CLion
cmake-build-*
.idea
