projectM -- Milkdrop-esque visualisation SDK
Copyright (C)2003-2019 projectM Team

This library is free software; you can redistribute it and/or
modify it under the terms of the GNU Lesser General Public
License as published by the Free Software Foundation; either
version 2.1 of the License, or (at your option) any later version.

This library is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public
License along with this library; if not, write to the Free Software
Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
See 'LICENSE.txt' included within this release

For the purposes of the copyright statement in the preamble of each source
code file comprising projectM, the projectM team are:

Carmelo Piccione
    Original Expression Parser/Evaluator
    Pulse Audio support (projectM-pulseaudio)
    Qt GUI (projectM-qt)

Peter Sperl
    Beat Detection
    Rendering
    XMMS Support (projectM-xmms)
    libvisual Support (projectM-libvisual)

Alligator Descartes
    Media Player Support (iTunes, Winamp, Windows Media Player)
    Test application frameworks (projectM-wxvis, projectM-sdlvis)
    DVD application (projectM-movie)
    Win32 screensaver (projectM-screensaver)

Roger Dan<PERSON>burg
    Advice & Support

Matthias Klumpp
    Original CMake build system
    Distro integration
    Bug fixes

Mischa Spiegelmock
    OSX native iTunes visualization plugin
    Preliminary web support (projectM-emscripten)
    CMake improvements for OSX and Linux
    SDL
    OpenGLES

Robert Pancoast
    Modernize win32 Build
    Windows Universal Application
    Provide XBOX Support
    Input Windows Audio Loopback with WASAPI

Kai Blaschke
    Updated CMake build system
    New Bison-/Flex-based Expression Evaluator (projectm-eval)
    C API
    Modernized Milkdrop 2 Renderer

Blaque Allen
    Rust Crates
    Emscripten Improvements
    Code Reviews

Dane Wagner
    Improved Audio Processing
    HLSL Shader Translation Fixes
    Sphinx Documentation

...and many others!

For a full list, please see the contributors page on GitHub:
https://github.com/projectM-visualizer/projectm/graphs/contributors