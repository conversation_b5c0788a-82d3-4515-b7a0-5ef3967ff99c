Checks: '
-*,

readability-*,
-readability-named-parameter,
-readability-magic-numbers,
-readability-make-member-function-const,

cppcoreguidelines-*,
-cppcoreguidelines-owning-memory,
-cppcoreguidelines-pro-type-union-access,
-cppcoreguidelines-avoid-magic-numbers,
-cppcoreguidelines-pro-bounds-constant-array-index,
-cppcoreguidelines-pro-bounds-pointer-arithmetic,

bugprone-*,
-bugprone-easily-swappable-parameters,

modernize-*,
performance-*,
misc-*'

CheckOptions:
  - key: readability-identifier-naming.VariableCase
    value: camelBack
  - key: readability-identifier-naming.LocalVariableCase
    value: camelBack
  - key: readability-identifier-naming.ParameterCase
    value: camelBack
  - key: readability-identifier-naming.ClassCase
    value: CamelCase
  - key: readability-identifier-naming.StructCase
    value: CamelCase
  - key: readability-identifier-naming.StructIgnoredRegexp
    value: ^projectm_.*
  - key: readability-identifier-naming.MemberCase
    value: camelBack
  - key: readability-identifier-naming.PublicMethodCase
    value: CamelCase
  - key: readability-identifier-naming.ProtectedMethodCase
    value: CamelCase
  - key: readability-identifier-naming.PrivateMethodCase
    value: CamelCase
  - key: readability-identifier-naming.MethodCase
    value: CamelCase
  - key: readability-identifier-naming.FunctionCase
    value: CamelCase
  - key: readability-identifier-naming.GlobalFunctionCase
    value: CamelCase
  - key: readability-identifier-naming.GlobalFunctionIgnoredRegexp
    value: ^projectm_.*
  - key: readability-identifier-naming.ScopedEnumConstantCase
    value: CamelCase
  - key: readability-identifier-naming.MacroDefinitionCase
    value: UPPER_CASE
  - key: readability-identifier-naming.PrivateMemberCase
    value: camelBack
  - key: readability-identifier-naming.ProtectedMemberPrefix
    value: m_
  - key: readability-identifier-naming.PrivateMemberPrefix
    value: m_

  - key: readability-uppercase-literal-suffix.NewSuffixes
    value: L;LL;Lu;LLu
