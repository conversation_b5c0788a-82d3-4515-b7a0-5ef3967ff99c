projectM SDL2 Test UI
=====================

Previously, this app was being released as the cross-platform projectM standalone application. With a completely
rewritten end-user application, which now resides in a separate repository, the original SDL2 application was kept
alongside libprojectM as a test UI for development purposes only.

It will not be installed and can be run from within the build directory to allow for convenient debugging without the
need to check out and build a separate repository.

The rewritten standalone SDL2 application for end users can now be found here:

https://github.com/projectM-visualizer/frontend-sdl2