prefix=@PKGCONFIG_PREFIX@
exec_prefix=${prefix}
libdir=@PKGCONFIG_LIB_DIR@
includedir=@PKGCONFIG_INCLUDE_DIR@
pkgdatadir=@PKGCONFIG_DATADIR_PATH@
sysconfdir=@PKGCONFIG_DATADIR_PATH@

Name: @PKGCONFIG_PACKAGE_NAME@
Version: @PROJECT_VERSION@
Description: @PKGCONFIG_PACKAGE_DESCRIPTION@
Requires: @PKGCONFIG_PACKAGE_REQUIREMENTS@
Libs: -L${libdir} -l:@PKGCONFIG_PROJECTM_LIBRARY@ @PKGCONFIG_LIBS@
Cflags: -I${includedir} @PKGCONFIG_FLAGS@
